**总体任务**

任务的核心是根据一份《数据收集说明》文档中的规则，处理一个名为 `dir_raw(1).xlsx`的Excel原始数据文件，并生成一份经过筛选、分析和标注的最终结果表格。

### **一、 数据预处理与筛选**

1. **数据范围**: 仅处理原始数据中从起始行到 `87275`行之前的数据子集，之后的数据可以删除。
2. **国籍筛选**:
   * **规则**: 不收集外国人信息。
   * **操作**: 对 `Nationality`（国籍）列进行筛选，只保留值为“中华人民共和国”的行。所有其他国籍（如巴西、法国等）以及国籍为空白的行都需要被删除。
3. **简历缺失筛选**:
   * **规则**: 不收集简历信息全部缺失的人员。
   * **操作**: 检查 `Resume`（简历）列，如果一个人的所有记录中，`Resume`列完全为空，则该人员的所有相关行都应被删除。

### **二、 海外背景信息处理（核心逻辑）**

此部分是任务的关键，需要根据 `OverseasBack`（海外背景）列的已有标注，并结合 `University`（毕业院校）和 `Resume`（简历）列的信息进行深度分析和修正，最终生成三个新的结果列：**海外经历**、**受教育国**、**工作国**。

1. **`OverseasBack`列的初始值含义**:

   * **1**: 代表有海外任职（工作）经历。
   * **2**: 代表有海外求学（教育）经历。
   * **3**: 代表无海外背景。
   * **空白**: 代表未知，需要手动分析。
2. **处理规则**:

   * **排除无海外背景**: `OverseasBack`列的值为 `3`的行，可以直接判定为无海外背景，并予以排除。
   * **分析空白及已有标注**: 对于 `OverseasBack`列为空白、值为 `1`或 `2`的行，需要进行以下层级的判断：
     * **优先级1：判断海外教育背景**
       * 首先检查 `University`列和 `Resume`列，识别其中是否包含海外院校的名称。
       * 如果能识别出海外求学经历，则将最终结果的 `海外经历`列标注为“**教育**”，并在 `受教育国`列填入对应的国家名称。**一旦确定有海外教育背景，则处理流程结束，无需再判断工作背景。**
     * **优先级2：判断海外工作背景**
       * 如果在第一步中**未发现**任何海外教育背景，则接着检查 `Resume`列，识别其中是否包含海外任职（工作）的经历。
       * **注意**: 需要区分国内企业使用的英文名和真正的海外企业，避免误判。
       * 如果能识别出海外工作经历，则将最终结果的 `海外经历`列标注为“**工作**”，并在 `工作国`列填入对应的国家名称。
   * **最终判定**:
     * **无海外经历**: 如果经过上述所有步骤，在 `Resume`和 `University`列中均未发现任何海外教育或工作信息，则 `海外经历`列标注为“**无**”。
     * **标注错误 (WRONG)**: 如果原始数据中 `OverseasBack`列的值（1或2）与通过简历分析得出的结论相悖，则 `海外经历`列标注为“**WRONG**”。
     * **信息模糊 (VAGUE)**: 如果能判断出有海外经历，但无法从现有信息中明确具体是哪个国家，则 `海外经历`列标注为“**VAGUE**”。

### **三、 最终输出要求**

1. **数据合并**: 原始数据中同一个 `PersonID`可能对应多行记录，最终输出的结果表中，每个 `PersonID`只应保留**一行**汇总信息，保留信息最多的那一行。
2. **结果列**: 最终的表格需要在原始数据的基础上，至少包含以下三个新生成的列：
   * `海外经历` (可能的值: 教育, 工作, 无, WRONG, VAGUE)
   * `受教育国` (国家名称)
   * `工作国` (国家名称)
3. **国家名称规范**: 所有提取出的国家名称，必须与提供的 `ISO 3166-1`国家代码表中的中文名称保持一致。
